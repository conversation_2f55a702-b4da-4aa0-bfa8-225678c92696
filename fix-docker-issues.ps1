#!/usr/bin/env pwsh
# Docker问题修复脚本
# 自动诊断和修复常见的Docker服务问题

param(
    [switch]$Force,
    [switch]$Verbose,
    [switch]$SkipBuild
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 检查并创建必要的目录
function New-RequiredDirectories {
    Write-Header "创建必要的目录"
    
    $directories = @(
        "data",
        "data/mysql",
        "data/redis", 
        "data/minio",
        "data/chroma",
        "data/elasticsearch",
        "data/uploads",
        "data/uploads/assets",
        "data/uploads/knowledge",
        "data/uploads/asset-library",
        "data/outputs",
        "data/outputs/renders",
        "data/models",
        "data/scene-generation",
        "data/scene-templates",
        "data/logs",
        "data/logs/scene-generation",
        "data/logs/monitoring",
        "data/prometheus",
        "data/grafana",
        "logs",
        "uploads",
        "temp",
        "backups"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Info "创建目录: $dir"
        }
    }
    
    Write-Success "目录结构检查完成"
}

# 修复权限问题
function Fix-Permissions {
    Write-Header "修复目录权限"
    
    $directories = @("data", "logs", "uploads", "temp", "backups")
    
    foreach ($dir in $directories) {
        if (Test-Path $dir) {
            try {
                # 在Windows上设置完全控制权限
                icacls $dir /grant "Everyone:(OI)(CI)F" /T | Out-Null
                Write-Info "修复权限: $dir"
            } catch {
                Write-Warning "无法修复权限: $dir - $($_.Exception.Message)"
            }
        }
    }
    
    Write-Success "权限修复完成"
}

# 清理Docker资源
function Clear-DockerResources {
    Write-Header "清理Docker资源"
    
    if ($Force) {
        Write-Warning "强制清理所有Docker资源..."
        
        # 停止所有容器
        docker-compose -f docker-compose.windows.yml down -v --remove-orphans
        
        # 清理未使用的资源
        docker system prune -f
        docker volume prune -f
        docker network prune -f
        
        Write-Success "Docker资源清理完成"
    } else {
        Write-Info "仅清理项目相关资源..."
        docker-compose -f docker-compose.windows.yml down --remove-orphans
        Write-Success "项目资源清理完成"
    }
}

# 检查环境文件
function Test-EnvironmentFile {
    Write-Header "检查环境配置文件"
    
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.windows.example") {
            Write-Info "复制 .env.windows.example 为 .env"
            Copy-Item ".env.windows.example" ".env"
            Write-Success "环境文件创建成功"
        } else {
            Write-Warning "环境文件 .env 不存在，将使用默认配置"
        }
    } else {
        Write-Success "环境文件 .env 存在"
    }
}

# 修复网络问题
function Fix-NetworkIssues {
    Write-Header "修复网络问题"
    
    try {
        # 检查网络是否存在
        $networkExists = docker network ls --filter name=dl-engine-network --format "{{.Name}}" | Select-String "dl-engine-network"
        
        if (-not $networkExists) {
            Write-Info "创建Docker网络..."
            docker network create dl-engine-network --driver bridge --subnet=**********/16 --gateway=**********
            Write-Success "Docker网络创建成功"
        } else {
            Write-Success "Docker网络已存在"
        }
    } catch {
        Write-Warning "网络修复失败: $($_.Exception.Message)"
    }
}

# 构建镜像
function Build-DockerImages {
    Write-Header "构建Docker镜像"
    
    if ($SkipBuild) {
        Write-Info "跳过镜像构建"
        return
    }
    
    try {
        Write-Info "构建所有服务镜像..."
        docker-compose -f docker-compose.windows.yml build --no-cache
        Write-Success "镜像构建完成"
    } catch {
        Write-Error "镜像构建失败: $($_.Exception.Message)"
        throw
    }
}

# 启动基础设施服务
function Start-InfrastructureServices {
    Write-Header "启动基础设施服务"
    
    $services = @("mysql", "redis", "minio", "elasticsearch", "chroma")
    
    foreach ($service in $services) {
        Write-Info "启动服务: $service"
        docker-compose -f docker-compose.windows.yml up -d $service
        Start-Sleep -Seconds 10
    }
    
    Write-Info "等待基础设施服务启动..."
    Start-Sleep -Seconds 60
    Write-Success "基础设施服务启动完成"
}

# 启动应用服务
function Start-ApplicationServices {
    Write-Header "启动应用服务"
    
    # 按依赖顺序启动服务
    $serviceGroups = @(
        @("service-registry"),
        @("user-service", "project-service", "asset-service"),
        @("api-gateway"),
        @("editor")
    )
    
    foreach ($group in $serviceGroups) {
        foreach ($service in $group) {
            Write-Info "启动服务: $service"
            docker-compose -f docker-compose.windows.yml up -d $service
            Start-Sleep -Seconds 15
        }
        Write-Info "等待服务组启动..."
        Start-Sleep -Seconds 30
    }
    
    Write-Success "应用服务启动完成"
}

# 验证服务状态
function Test-ServiceStatus {
    Write-Header "验证服务状态"
    
    Write-Info "运行服务测试脚本..."
    if (Test-Path "test-docker-services.ps1") {
        & .\test-docker-services.ps1 -Timeout 15
    } else {
        Write-Warning "测试脚本不存在，手动检查服务状态..."
        docker-compose -f docker-compose.windows.yml ps
    }
}

# 主函数
function Main {
    Write-Header "Docker问题修复工具"
    
    try {
        # 检查Docker是否运行
        docker info | Out-Null
        Write-Success "Docker Desktop 运行正常"
    } catch {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    
    # 执行修复步骤
    New-RequiredDirectories
    Fix-Permissions
    Test-EnvironmentFile
    Clear-DockerResources
    Fix-NetworkIssues
    
    if (-not $SkipBuild) {
        Build-DockerImages
    }
    
    Start-InfrastructureServices
    Start-ApplicationServices
    Test-ServiceStatus
    
    Write-Header "修复完成"
    Write-Success "🎉 Docker服务修复完成！"
    Write-Info "前端编辑器: http://localhost:80"
    Write-Info "API网关: http://localhost:3000"
    Write-Info "如果仍有问题，请查看容器日志: docker-compose -f docker-compose.windows.yml logs [service-name]"
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "修复过程中发生错误: $($_.Exception.Message)"
    Write-Info "💡 建议操作："
    Write-Info "1. 检查Docker Desktop是否正常运行"
    Write-Info "2. 确保有足够的磁盘空间和内存"
    Write-Info "3. 尝试重启Docker Desktop"
    Write-Info "4. 运行: .\fix-docker-issues.ps1 -Force -SkipBuild"
    exit 1
}
