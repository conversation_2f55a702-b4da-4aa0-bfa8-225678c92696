{"common": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "search": "Search", "loading": "Loading", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "upload": "Upload", "download": "Download", "import": "Import", "export": "Export", "add": "Add", "remove": "Remove", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "select": "Select", "selectAll": "Select All", "deselect": "Deselect", "rename": "<PERSON><PERSON>", "duplicate": "Duplicate", "settings": "Settings", "preferences": "Preferences", "help": "Help", "about": "About", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "username": "Username", "password": "Password", "email": "Email", "phone": "Phone", "address": "Address", "description": "Description", "name": "Name", "title": "Title", "content": "Content", "type": "Type", "status": "Status", "date": "Date", "time": "Time", "size": "Size", "color": "Color", "position": "Position", "rotation": "Rotation", "scale": "Scale", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "depth": "De<PERSON><PERSON>", "radius": "<PERSON><PERSON>", "opacity": "Opacity", "visible": "Visible", "hidden": "Hidden", "enabled": "Enabled", "disabled": "Disabled", "locked": "Locked", "unlocked": "Unlocked", "public": "Public", "private": "Private", "shared": "Shared", "owner": "Owner", "creator": "Creator", "createdAt": "Created At", "updatedAt": "Updated At", "version": "Version", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "auto": "Auto", "file": "File", "folder": "Folder", "project": "Project", "scene": "Scene", "asset": "<PERSON><PERSON>", "material": "Material", "texture": "Texture", "model": "Model", "animation": "Animation", "audio": "Audio", "video": "Video", "script": "<PERSON><PERSON><PERSON>", "shader": "Shader", "particle": "Particle", "physics": "Physics", "camera": "Camera", "environment": "Environment", "skybox": "Skybox", "terrain": "Terrain", "water": "Water", "vegetation": "Vegetation", "character": "Character", "vehicle": "Vehicle", "weapon": "Weapon", "effect": "Effect", "ui": "UI", "hud": "HUD", "menu": "<PERSON><PERSON>", "button": "<PERSON><PERSON>", "input": "Input", "slider": "Slide<PERSON>", "checkbox": "Checkbox", "radioButton": "Radio Button", "dropdown": "Dropdown", "panel": "Panel", "window": "Window", "dialog": "Dialog", "tooltip": "<PERSON><PERSON><PERSON>", "notification": "Notification", "message": "Message", "alert": "<PERSON><PERSON>", "prompt": "Prompt", "progress": "Progress", "profile": "Profile", "noNotifications": "No notifications", "projects": "Projects", "editor": "Editor", "github": "GitHub", "guest": "Guest"}, "editor": {"title": "IR Engine Editor", "newProject": "New Project", "openProject": "Open Project", "saveProject": "Save Project", "saveProjectAs": "Save Project As", "closeProject": "Close Project", "importAsset": "Import Asset", "exportScene": "Export Scene", "publishProject": "Publish Project", "projectSettings": "Project Settings", "editorSettings": "Editor Settings", "userSettings": "User Settings", "sceneView": "Scene View", "gameView": "Game View", "assetView": "Asset View", "hierarchyView": "Hierarchy View", "inspectorView": "Inspector View", "consoleView": "Console View", "projectView": "Project View", "animationView": "Animation View", "physicsView": "Physics View", "lightingView": "Lighting View", "renderingView": "Rendering View", "audioView": "Audio View", "particleView": "Particle View", "terrainView": "Terrain View", "vegetationView": "Vegetation View", "waterView": "Water View", "weatherView": "Weather View", "postProcessingView": "Post Processing View", "terrain": {"terrainEditor": "Terrain Editor", "noTerrainComponent": "Selected entity has no terrain component", "addTerrainComponent": "Add Terrain Component", "createSuccess": "Terrain created successfully", "createFailed": "Failed to create terrain", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete this terrain? This action cannot be undone.", "deleteSuccess": "Terrain deleted successfully", "deleteFailed": "Failed to delete terrain", "generation": "Generation", "sculpting": "Sculpting", "preview": "Preview"}, "uiView": "UI View", "scriptView": "Script View", "shaderView": "Shader View", "debugView": "Debug View", "profileView": "Profile View", "statisticsView": "Statistics View", "historyView": "History View", "bookmarkView": "Bookmark View", "searchView": "Search View", "helpView": "Help View", "aboutView": "About View", "welcomeView": "Welcome View", "loginView": "Login View", "registerView": "Register View", "teamView": "Team View", "collaborationView": "Collaboration View", "chatView": "Chat View", "notificationView": "Notification View", "activityView": "Activity View", "settingsView": "Settings View", "preferencesView": "Preferences View", "themeView": "Theme View", "languageView": "Language View", "shortcutView": "Shortcut View", "pluginView": "Plugin View", "extensionView": "Extension View", "marketplaceView": "Marketplace View", "storeView": "Store View", "communityView": "Community View", "forumView": "Forum View", "documentationView": "Documentation View", "tutorialView": "Tutorial View", "exampleView": "Example View", "templateView": "Template View", "assetStoreView": "Asset Store View", "pluginStoreView": "Plugin Store View", "extensionStoreView": "Extension Store View", "panel": {"close": "Close", "maximize": "Maximize", "restore": "Rest<PERSON>", "pin": "<PERSON>n", "unpin": "Unpin", "hide": "<PERSON>de", "show": "Show", "unknownContent": "Unknown Content"}, "panels": {"hierarchy": "Hierarchy", "inspector": "Inspector", "assets": "Assets", "scene": "Scene", "console": "<PERSON><PERSON><PERSON>", "animation": "Animation", "physics": "Physics", "particle": "Particle", "layers": "Layers", "instances": "Instances"}, "layers": {"add": "Add Layer", "addGroup": "Add Group", "edit": "Edit Layer", "delete": "Delete Layer", "moveUp": "Move Up", "moveDown": "Move Down", "search": "Search Layers", "name": "Layer Name", "nameRequired": "Layer name is required", "groupName": "Group Name", "groupNameRequired": "Group name is required", "color": "Layer Color", "tags": "Tags", "parent": "<PERSON><PERSON>er", "selectParent": "Select Parent Layer", "createLayer": "Create Layer", "createGroup": "Create Layer Group", "editLayer": "Edit Layer", "confirmDelete": "Confirm Delete", "confirmDeleteContent": "Are you sure you want to delete layer \"{name}\"?", "addLayerToGroup": "Add Layer to Group", "addGroupToGroup": "Add Subgroup"}, "instances": {"addTemplate": "Add Template", "addInstance": "Add Instance", "edit": "Edit Instance", "delete": "Delete Instance", "parameters": "Parameters", "search": "Search Templates and Instances", "templates": "Templates", "instances": "Instances", "templateName": "Template Name", "templateNameRequired": "Template name is required", "templateDescription": "Template Description", "instanceName": "Instance Name", "instanceNameRequired": "Instance name is required", "template": "Template", "templateRequired": "Template is required", "position": "Position", "rotation": "Rotation", "scale": "Scale", "createTemplate": "Create Template", "createInstance": "Create Instance", "editInstance": "Edit Instance", "confirmDeleteTemplate": "Confirm Delete Template", "confirmDeleteTemplateContent": "Are you sure you want to delete template \"{name}\"?", "confirmDeleteInstance": "Confirm Delete Instance", "confirmDeleteInstanceContent": "Are you sure you want to delete instance \"{name}\"?", "cannotDeleteTemplate": "Cannot Delete Template", "templateInUse": "This template is in use by instances and cannot be deleted", "wallColor": "Wall Color", "floorColor": "Floor Color", "ceilingColor": "Ceiling Color", "windowCount": "Window Count", "deskColor": "Desk Color", "chairColor": "Chair Color", "hasComputer": "Has Computer"}, "layout": {"manage": "Manage Layouts", "reset": "Reset Layout", "save": "Save Layout", "loadLayout": "Load Layout", "saveLayout": "Save Layout", "layoutName": "Layout Name", "nameRequired": "Layout name is required", "enterName": "Enter layout name", "darkTheme": "Switch to Dark Theme", "lightTheme": "Switch to Light Theme"}}, "collaboration": {"title": "Collaboration", "enable": "Enable Collaboration", "disabled": "Collaboration is disabled", "inviteOthers": "Invite others to join", "copyLink": "Copy Link", "inviteLinkCopied": "Invitation link copied to clipboard", "you": "you", "noUsers": "No online users", "noOperations": "No operation history", "settingsDescription": "Collaboration settings control permissions and synchronization behavior", "tabs": {"users": "Users", "history": "History", "settings": "Settings"}, "status": {"connected": "Connected", "connecting": "Connecting", "disconnected": "Disconnected", "error": "Connection Error", "active": "Active", "inactive": "Idle"}, "roles": {"owner": "Owner", "admin": "Admin", "editor": "Editor", "viewer": "Viewer"}, "operations": {"entityCreate": "Create Entity", "entityUpdate": "Update Entity", "entityDelete": "Delete Entity", "componentAdd": "Add Component", "componentUpdate": "Update Component", "componentRemove": "Remove Component", "sceneUpdate": "Update Scene", "cursorMove": "Move Cursor", "selectionChange": "Change Selection"}, "operationDescriptions": {"entityCreate": "Created entity \"{name}\"", "entityUpdate": "Updated entity \"{name}\"", "entityDelete": "Deleted entity \"{name}\"", "componentAdd": "Added \"{component}\" component to entity \"{entity}\"", "componentUpdate": "Updated \"{component}\" component on entity \"{entity}\"", "componentRemove": "Removed \"{component}\" component from entity \"{entity}\"", "sceneUpdate": "Updated scene settings", "cursorMove": "Moved cursor", "selectionChange": "Selected {count} objects", "unknown": "Unknown operation"}, "errors": {"noProjectOrScene": "No project or scene selected", "connectionFailed": "Connection failed", "unauthorized": "Unauthorized", "permissionDenied": "Permission denied", "copyFailed": "Co<PERSON> failed"}}, "auth": {"loginTitle": "User Login", "loginSubtitle": "Please enter your account information", "registerTitle": "User Registration", "registerSubtitle": "Create your new account", "forgotPasswordTitle": "Forgot Password", "forgotPasswordSubtitle": "Please enter your email address", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "username": "Username", "password": "Password", "email": "Email", "confirmPassword": "Confirm Password", "confirmPasswordRequired": "Please confirm password", "confirmPasswordPlaceholder": "Please confirm password", "passwordMismatch": "Passwords do not match", "rememberMe": "Remember Me", "loginSuccess": "Login Successful", "loginFailed": "Login Failed", "registerSuccess": "Registration Successful", "registerFailed": "Registration Failed", "logoutSuccess": "Logout Successful", "logoutFailed": "Logout Failed", "resetPasswordSuccess": "Password Reset Successful", "resetPasswordFailed": "Password Reset Failed", "changePasswordSuccess": "Password Change Successful", "changePasswordFailed": "Password Change Failed", "passwordNotMatch": "Passwords do not match", "passwordTooShort": "Password is too short", "passwordTooWeak": "Password is too weak", "emailInvalid": "Invalid email", "emailRequired": "Please enter email", "emailPlaceholder": "Please enter email", "passwordRequired": "Please enter password", "passwordPlaceholder": "Please enter password", "usernameRequired": "Please enter username", "usernameTooShort": "Username must be at least 3 characters", "usernamePlaceholder": "Please enter username", "agreementRequired": "Please agree to the terms and privacy policy", "agreement": "I agree to the", "terms": "Terms of Service", "privacy": "Privacy Policy", "and": "and", "haveAccount": "Already have an account?", "noAccount": "Don't have an account?", "orLoginWith": "Or login with", "orRegisterWith": "Or register with", "usernameInvalid": "Invalid username", "usernameTaken": "Username is already taken", "emailTaken": "Email is already taken", "accountNotFound": "Account not found", "accountLocked": "Account is locked", "accountDisabled": "Account is disabled", "accountExpired": "Account has expired", "sessionExpired": "Session has expired", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "tooManyRequests": "Too many requests", "serverError": "Server error", "networkError": "Network error", "unknownError": "Unknown error"}, "git": {"title": "Version Control", "status": "Status", "history": "History", "branches": "Branches", "loading": "Loading...", "refreshSuccess": "Refresh successful", "searchCommits": "Search commits", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "loadingCommits": "Loading commit history...", "resolveConflicts": "Resolve Conflicts", "resolveAllConfirmTitle": "Confirm Resolve All Conflicts", "resolveAllConfirmContent": "Are you sure you want to resolve all conflicts using {choice} strategy?", "resolveAll": "Resolve All", "allConflictsResolved": "All conflicts resolved", "save": "Save", "edit": "Edit", "ours": "Ours", "theirs": "Theirs", "both": "Both", "custom": "Custom"}, "projects": {"title": "Projects", "allProjects": "All Projects", "myProjects": "My Projects", "shared": "Shared Projects", "search": "Search Projects", "new": "New Project", "newProject": "New Project", "newScene": "New Scene", "open": "Open", "edit": "Edit", "delete": "Delete", "public": "Public", "private": "Private", "gridView": "Grid View", "listView": "List View", "noProjects": "No projects", "noSearchResults": "No matching projects found", "createFirst": "Create First Project", "noMyProjects": "You haven't created any projects yet", "noSharedProjects": "No shared projects", "searchPlaceholder": "Search projects...", "categoryFilter": "Category Filter", "noProjectsFound": "No projects found", "favorites": "Favorites", "recent": "Recent", "createProject": "Create Project", "name": "Project Name", "nameRequired": "Please enter project name", "namePlaceholder": "Please enter project name", "description": "Project Description", "descriptionPlaceholder": "Please enter project description", "visibility": "Project Visibility", "sceneName": "Scene Name", "sceneNameRequired": "Please enter scene name", "sceneNamePlaceholder": "Please enter scene name", "sceneDescription": "Scene Description", "sceneDescriptionPlaceholder": "Please enter scene description", "createSuccess": "Project created successfully", "createError": "Failed to create project", "sceneCreateSuccess": "Scene created successfully", "sceneCreateError": "Failed to create scene", "deleteSuccess": "Project deleted successfully", "deleteError": "Failed to delete project", "deleteConfirmTitle": "Confirm Delete Project", "deleteConfirmMessage": "Are you sure you want to delete project \"{name}\"?", "deleteConfirmWarning": "This action cannot be undone. All data in the project will be permanently deleted."}, "menu": {"file": "File", "edit": "Edit", "view": "View", "window": "Window", "help": "Help", "tutorials": "Tutorials", "documentation": "Documentation", "about": "About", "project": "Project", "settings": "Settings"}, "tutorials": {"types": {"interactive": "Interactive", "video": "Video", "example": "Example", "unknown": "Unknown"}, "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "Next", "popular": "Popular", "duration": "Duration", "minutes": "minutes", "prerequisitesRequired": "Prerequisites required", "locked": "Locked", "prerequisites": "Prerequisites", "review": "Review", "start": "Start", "seriesCompleted": "Series Completed", "certificate": "Certificate", "congratulations": "Congratulations!", "certificateText": "Congratulations {name} for completing the {series} tutorial series!", "series": "Series", "completedTutorials": "Completed Tutorials", "completedDate": "Completion Date", "seriesNotFound": "Tutorial series not found", "items": "items", "tags": "tags", "completed": "completed", "tutorialsCompleted": "tutorials completed"}}