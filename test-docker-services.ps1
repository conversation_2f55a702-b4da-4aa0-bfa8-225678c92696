#!/usr/bin/env pwsh
# Docker服务测试脚本
# 用于验证所有微服务是否正常启动并可访问

param(
    [switch]$Verbose,
    [switch]$Fix,
    [int]$Timeout = 30
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🚀 $message"
    Write-Host "=" * 60
}

# 测试HTTP端点
function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$TimeoutSeconds = 10
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "$ServiceName 健康检查通过 ($Url)"
            return $true
        } else {
            Write-Warning "$ServiceName 返回状态码: $($response.StatusCode) ($Url)"
            return $false
        }
    } catch {
        Write-Error "$ServiceName 健康检查失败: $($_.Exception.Message) ($Url)"
        return $false
    }
}

# 检查Docker容器状态
function Test-DockerContainers {
    Write-Header "检查Docker容器状态"
    
    $containers = @(
        "dl-engine-mysql-win",
        "dl-engine-redis-win", 
        "dl-engine-minio-win",
        "dl-engine-chroma-win",
        "dl-engine-elasticsearch-win",
        "dl-engine-service-registry-win",
        "dl-engine-api-gateway-win",
        "dl-engine-user-service-win",
        "dl-engine-project-service-win",
        "dl-engine-asset-service-win",
        "dl-engine-editor-win"
    )
    
    $allHealthy = $true
    
    foreach ($container in $containers) {
        try {
            $status = docker inspect $container --format '{{.State.Status}}' 2>$null
            $health = docker inspect $container --format '{{.State.Health.Status}}' 2>$null
            
            if ($status -eq "running") {
                if ($health -eq "healthy" -or $health -eq "<no value>") {
                    Write-Success "$container: 运行中 (健康)"
                } else {
                    Write-Warning "$container: 运行中 (健康状态: $health)"
                    $allHealthy = $false
                }
            } else {
                Write-Error "$container: $status"
                $allHealthy = $false
            }
        } catch {
            Write-Error "$container: 未找到或无法访问"
            $allHealthy = $false
        }
    }
    
    return $allHealthy
}

# 测试API端点
function Test-ApiEndpoints {
    Write-Header "测试API端点"
    
    $endpoints = @(
        @{ Url = "http://localhost:3000/api/health"; Name = "API网关健康检查" },
        @{ Url = "http://localhost:4010/api/health"; Name = "服务注册中心健康检查" },
        @{ Url = "http://localhost:4001/api/health"; Name = "用户服务健康检查" },
        @{ Url = "http://localhost:4002/api/health"; Name = "项目服务健康检查" },
        @{ Url = "http://localhost:4003/api/health"; Name = "资产服务健康检查" },
        @{ Url = "http://localhost:9000/minio/health/live"; Name = "MinIO健康检查" },
        @{ Url = "http://localhost:9200/_cluster/health"; Name = "Elasticsearch健康检查" },
        @{ Url = "http://localhost:8000/api/v1/heartbeat"; Name = "Chroma健康检查" },
        @{ Url = "http://localhost:80"; Name = "前端编辑器" }
    )
    
    $allHealthy = $true
    
    foreach ($endpoint in $endpoints) {
        $result = Test-HttpEndpoint -Url $endpoint.Url -ServiceName $endpoint.Name -TimeoutSeconds $Timeout
        if (-not $result) {
            $allHealthy = $false
        }
        Start-Sleep -Milliseconds 500
    }
    
    return $allHealthy
}

# 测试前端API调用
function Test-FrontendApiCalls {
    Write-Header "测试前端API调用"
    
    $apiCalls = @(
        @{ Url = "http://localhost:3000/api/projects"; Name = "获取项目列表" },
        @{ Url = "http://localhost:3000/api/users"; Name = "获取用户列表" },
        @{ Url = "http://localhost:3000/api/assets"; Name = "获取资产列表" }
    )
    
    $allWorking = $true
    
    foreach ($call in $apiCalls) {
        try {
            $response = Invoke-WebRequest -Uri $call.Url -TimeoutSec $Timeout -UseBasicParsing
            if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 401) {
                Write-Success "$($call.Name): API可访问 (状态码: $($response.StatusCode))"
            } else {
                Write-Warning "$($call.Name): 状态码 $($response.StatusCode)"
                $allWorking = $false
            }
        } catch {
            Write-Error "$($call.Name): $($_.Exception.Message)"
            $allWorking = $false
        }
        Start-Sleep -Milliseconds 500
    }
    
    return $allWorking
}

# 主函数
function Main {
    Write-Header "Docker服务健康检查"
    
    # 检查Docker是否运行
    try {
        docker info | Out-Null
        Write-Success "Docker Desktop 运行正常"
    } catch {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    
    # 检查容器状态
    $containersHealthy = Test-DockerContainers
    
    # 等待服务启动
    if (-not $containersHealthy) {
        Write-Warning "部分容器未正常运行，等待30秒后重试..."
        Start-Sleep -Seconds 30
    }
    
    # 测试API端点
    $endpointsHealthy = Test-ApiEndpoints
    
    # 测试前端API调用
    $apiCallsWorking = Test-FrontendApiCalls
    
    # 总结
    Write-Header "测试结果总结"
    
    if ($containersHealthy -and $endpointsHealthy -and $apiCallsWorking) {
        Write-Success "🎉 所有服务运行正常！"
        Write-Info "前端编辑器: http://localhost:80"
        Write-Info "API网关: http://localhost:3000"
        Write-Info "MinIO控制台: http://localhost:9001"
        exit 0
    } else {
        Write-Error "❌ 部分服务存在问题"
        
        if ($Fix) {
            Write-Info "尝试修复问题..."
            # 这里可以添加自动修复逻辑
        }
        
        Write-Info "💡 建议操作："
        Write-Info "1. 检查Docker容器日志: docker-compose -f docker-compose.windows.yml logs [service-name]"
        Write-Info "2. 重启有问题的服务: docker-compose -f docker-compose.windows.yml restart [service-name]"
        Write-Info "3. 完全重启系统: .\start-windows.ps1 -Clean -Build"
        exit 1
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
